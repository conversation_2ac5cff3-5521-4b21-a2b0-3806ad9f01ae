package com.midea.logistics.otplbs.bean;

import com.midea.logistics.iflow.mbmp.domain.dto.ReqCreateDraftDto;
import com.midea.logistics.otp.order.domain.bean.MipFile;
import java.util.List;
import lombok.Data;

/**
 * @Author: dumg
 * @Date: 2024-04-16-14:18
 * Description:
 */
@Data
public class ReqCreateDraftExtDto extends ReqCreateDraftDto {
    //管控类型
    private String remark;
    private String capacityCode;
    private List<MipFile> fileList;
    private String applyReason;
}
